/* Responsive CSS for NoDicciSeña */

/* Base Mobile First Approach */
/* Default styles are for mobile (320px+) */

/* Compact Devices: 480px - 767px (Foldables outer, Large phones) */
@media (min-width: 480px) {
    .compact\:text-lg { font-size: 1.125rem; }
    .compact\:text-xl { font-size: 1.25rem; }
    .compact\:text-2xl { font-size: 1.5rem; }
    .compact\:text-3xl { font-size: 1.875rem; }
    .compact\:text-4xl { font-size: 2.25rem; }
    .compact\:text-5xl { font-size: 3rem; }
    
    .compact\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .compact\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    
    .compact\:flex-row { flex-direction: row; }
    .compact\:flex-col { flex-direction: column; }
    
    .compact\:hidden { display: none; }
    .compact\:block { display: block; }
    .compact\:flex { display: flex; }
    
    .compact\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
    .compact\:py-4 { padding-top: 1rem; padding-bottom: 1rem; }
    
    .compact\:space-x-4 > :not([hidden]) ~ :not([hidden]) { margin-left: 1rem; }
    .compact\:space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem; }
    
    /* Navigation adjustments */
    .compact\:nav-compact {
        padding: 0.75rem 1rem;
    }
    
    /* Hero section adjustments */
    .compact\:hero-padding {
        padding: 2rem 1rem;
    }
    
    /* Card adjustments */
    .compact\:card-spacing {
        gap: 1rem;
    }
}

/* Tablet Devices: 768px - 1023px (Tablets, Foldables inner) */
@media (min-width: 768px) {
    .tablet\:text-xl { font-size: 1.25rem; }
    .tablet\:text-2xl { font-size: 1.5rem; }
    .tablet\:text-3xl { font-size: 1.875rem; }
    .tablet\:text-4xl { font-size: 2.25rem; }
    .tablet\:text-5xl { font-size: 3rem; }
    .tablet\:text-6xl { font-size: 3.75rem; }
    
    .tablet\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .tablet\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .tablet\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    
    .tablet\:flex { display: flex; }
    .tablet\:hidden { display: none; }
    .tablet\:block { display: block; }
    
    .tablet\:px-8 { padding-left: 2rem; padding-right: 2rem; }
    .tablet\:py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
    
    /* Navigation for tablets */
    .tablet\:nav-expanded {
        padding: 1rem 1.5rem;
    }
    
    .tablet\:nav-items {
        gap: 0.5rem;
    }
    
    /* Hero adjustments */
    .tablet\:hero-grid {
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
    }
    
    /* Statistics grid */
    .tablet\:stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
    
    /* Pricing cards */
    .tablet\:pricing-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Laptop Devices: 1024px - 1279px (13-14" Laptops) */
@media (min-width: 1024px) {
    .laptop\:text-xl { font-size: 1.25rem; }
    .laptop\:text-2xl { font-size: 1.5rem; }
    .laptop\:text-3xl { font-size: 1.875rem; }
    .laptop\:text-4xl { font-size: 2.25rem; }
    .laptop\:text-5xl { font-size: 3rem; }
    .laptop\:text-6xl { font-size: 3.75rem; }
    .laptop\:text-7xl { font-size: 4.5rem; }
    
    .laptop\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .laptop\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .laptop\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .laptop\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
    
    .laptop\:flex { display: flex; }
    .laptop\:hidden { display: none; }
    .laptop\:block { display: block; }
    
    .laptop\:text-left { text-align: left; }
    .laptop\:text-center { text-align: center; }
    
    .laptop\:px-12 { padding-left: 3rem; padding-right: 3rem; }
    .laptop\:py-8 { padding-top: 2rem; padding-bottom: 2rem; }
    
    .laptop\:space-x-6 > :not([hidden]) ~ :not([hidden]) { margin-left: 1.5rem; }
    .laptop\:space-x-8 > :not([hidden]) ~ :not([hidden]) { margin-left: 2rem; }
    
    /* Navigation for laptops */
    .laptop\:nav-full {
        padding: 1rem 2rem;
    }
    
    .laptop\:nav-items-expanded {
        gap: 1rem;
    }
    
    /* Hero section */
    .laptop\:hero-content {
        max-width: 50%;
    }
    
    .laptop\:hero-visual {
        max-width: 45%;
    }
    
    /* Statistics */
    .laptop\:stats-grid-full {
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }
    
    /* Team grid */
    .laptop\:team-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
    
    /* Gallery grid */
    .laptop\:gallery-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
    
    /* Pricing full layout */
    .laptop\:pricing-full {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
}

/* Desktop Devices: 1280px - 1599px (Standard Desktops) */
@media (min-width: 1280px) {
    .desktop\:text-xl { font-size: 1.25rem; }
    .desktop\:text-2xl { font-size: 1.5rem; }
    .desktop\:text-3xl { font-size: 1.875rem; }
    .desktop\:text-4xl { font-size: 2.25rem; }
    .desktop\:text-5xl { font-size: 3rem; }
    .desktop\:text-6xl { font-size: 3.75rem; }
    .desktop\:text-7xl { font-size: 4.5rem; }
    .desktop\:text-8xl { font-size: 6rem; }
    
    .desktop\:flex { display: flex; }
    .desktop\:hidden { display: none; }
    .desktop\:block { display: block; }
    
    .desktop\:px-16 { padding-left: 4rem; padding-right: 4rem; }
    .desktop\:py-12 { padding-top: 3rem; padding-bottom: 3rem; }
    
    .desktop\:max-w-7xl { max-width: 80rem; }
    .desktop\:max-w-6xl { max-width: 72rem; }
    
    /* Enhanced spacing for desktop */
    .desktop\:space-x-8 > :not([hidden]) ~ :not([hidden]) { margin-left: 2rem; }
    .desktop\:space-x-12 > :not([hidden]) ~ :not([hidden]) { margin-left: 3rem; }
    
    /* Navigation enhancements */
    .desktop\:nav-enhanced {
        padding: 1.25rem 2.5rem;
    }
    
    /* Hero enhancements */
    .desktop\:hero-enhanced {
        padding: 4rem 0;
    }
    
    /* Content width constraints */
    .desktop\:content-width {
        max-width: 1200px;
        margin: 0 auto;
    }
}

/* QHD Devices: 1600px - 2559px (QHD/2K focus) */
@media (min-width: 1600px) {
    .qhd\:text-2xl { font-size: 1.5rem; }
    .qhd\:text-3xl { font-size: 1.875rem; }
    .qhd\:text-4xl { font-size: 2.25rem; }
    .qhd\:text-5xl { font-size: 3rem; }
    .qhd\:text-6xl { font-size: 3.75rem; }
    .qhd\:text-7xl { font-size: 4.5rem; }
    .qhd\:text-8xl { font-size: 6rem; }
    .qhd\:text-9xl { font-size: 8rem; }
    
    .qhd\:flex { display: flex; }
    .qhd\:hidden { display: none; }
    .qhd\:block { display: block; }
    
    .qhd\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .qhd\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
    .qhd\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
    
    .qhd\:px-20 { padding-left: 5rem; padding-right: 5rem; }
    .qhd\:py-16 { padding-top: 4rem; padding-bottom: 4rem; }
    
    .qhd\:max-w-8xl { max-width: 88rem; }
    .qhd\:max-w-9xl { max-width: 96rem; }
    
    /* Enhanced layouts for QHD */
    .qhd\:enhanced-spacing {
        gap: 3rem;
    }
    
    .qhd\:enhanced-padding {
        padding: 2rem;
    }
    
    /* Navigation for QHD */
    .qhd\:nav-premium {
        padding: 1.5rem 3rem;
    }
    
    /* Team grid enhancement */
    .qhd\:team-enhanced {
        grid-template-columns: repeat(4, 1fr);
        gap: 2.5rem;
    }
    
    /* Gallery enhancement */
    .qhd\:gallery-enhanced {
        grid-template-columns: repeat(5, 1fr);
        gap: 2rem;
    }
}

/* UHD Devices: 2560px+ (4K, Ultra-wide) */
@media (min-width: 2560px) {
    .uhd\:text-3xl { font-size: 1.875rem; }
    .uhd\:text-4xl { font-size: 2.25rem; }
    .uhd\:text-5xl { font-size: 3rem; }
    .uhd\:text-6xl { font-size: 3.75rem; }
    .uhd\:text-7xl { font-size: 4.5rem; }
    .uhd\:text-8xl { font-size: 6rem; }
    .uhd\:text-9xl { font-size: 8rem; }
    
    .uhd\:px-24 { padding-left: 6rem; padding-right: 6rem; }
    .uhd\:py-20 { padding-top: 5rem; padding-bottom: 5rem; }
    
    .uhd\:max-w-10xl { max-width: 104rem; }
    .uhd\:max-w-11xl { max-width: 112rem; }
    
    .uhd\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
    .uhd\:grid-cols-8 { grid-template-columns: repeat(8, minmax(0, 1fr)); }
    
    /* Ultra-wide specific layouts */
    .uhd\:ultra-spacing {
        gap: 4rem;
    }
    
    .uhd\:ultra-padding {
        padding: 3rem;
    }
    
    /* Navigation for UHD */
    .uhd\:nav-ultra {
        padding: 2rem 4rem;
    }
    
    /* Content scaling for UHD */
    .uhd\:scale-110 {
        transform: scale(1.1);
    }
    
    .uhd\:scale-125 {
        transform: scale(1.25);
    }
}

/* Responsive Typography Scale */
@media (max-width: 479px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
    h5 { font-size: 1.125rem; }
    h6 { font-size: 1rem; }
}

@media (min-width: 480px) and (max-width: 767px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.75rem; }
    h4 { font-size: 1.5rem; }
    h5 { font-size: 1.25rem; }
    h6 { font-size: 1.125rem; }
}

@media (min-width: 768px) and (max-width: 1023px) {
    h1 { font-size: 3rem; }
    h2 { font-size: 2.5rem; }
    h3 { font-size: 2rem; }
    h4 { font-size: 1.75rem; }
    h5 { font-size: 1.5rem; }
    h6 { font-size: 1.25rem; }
}

@media (min-width: 1024px) {
    h1 { font-size: 3.5rem; }
    h2 { font-size: 3rem; }
    h3 { font-size: 2.5rem; }
    h4 { font-size: 2rem; }
    h5 { font-size: 1.75rem; }
    h6 { font-size: 1.5rem; }
}

/* Responsive Spacing */
.responsive-padding {
    padding: 1rem;
}

@media (min-width: 480px) {
    .responsive-padding { padding: 1.5rem; }
}

@media (min-width: 768px) {
    .responsive-padding { padding: 2rem; }
}

@media (min-width: 1024px) {
    .responsive-padding { padding: 3rem; }
}

@media (min-width: 1280px) {
    .responsive-padding { padding: 4rem; }
}

/* Responsive Margins */
.responsive-margin {
    margin: 1rem 0;
}

@media (min-width: 480px) {
    .responsive-margin { margin: 1.5rem 0; }
}

@media (min-width: 768px) {
    .responsive-margin { margin: 2rem 0; }
}

@media (min-width: 1024px) {
    .responsive-margin { margin: 3rem 0; }
}

/* Container Responsive Widths */
.container-responsive {
    width: 100%;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 480px) {
    .container-responsive {
        max-width: 448px;
        padding: 0 1.5rem;
    }
}

@media (min-width: 768px) {
    .container-responsive {
        max-width: 736px;
        padding: 0 2rem;
    }
}

@media (min-width: 1024px) {
    .container-responsive {
        max-width: 992px;
        padding: 0 2.5rem;
    }
}

@media (min-width: 1280px) {
    .container-responsive {
        max-width: 1248px;
        padding: 0 3rem;
    }
}

@media (min-width: 1600px) {
    .container-responsive {
        max-width: 1568px;
        padding: 0 4rem;
    }
}

@media (min-width: 2560px) {
    .container-responsive {
        max-width: 2496px;
        padding: 0 6rem;
    }
}
