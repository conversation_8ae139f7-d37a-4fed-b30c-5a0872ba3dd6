// Navigation JavaScript for NoDicciSeña
document.addEventListener('DOMContentLoaded', function() {
    initializeResponsiveNavigation();
    initializeDropdownMenus();
    initializeNavigationIndicators();
    initializeScrollNavigation();
    initializeBreadcrumbs();
});

// Responsive Navigation System
function initializeResponsiveNavigation() {
    const mobileNav = document.getElementById('mobile-nav');
    const tabletNav = document.getElementById('tablet-nav');
    const desktopNav = document.getElementById('desktop-nav');
    const qhdNav = document.getElementById('qhd-nav');
    
    function updateNavigationVisibility() {
        const width = window.innerWidth;
        
        // Hide all navigations first
        [mobileNav, tabletNav, desktopNav, qhdNav].forEach(nav => {
            if (nav) nav.style.display = 'none';
        });
        
        // Show appropriate navigation based on screen size
        if (width < 480) {
            if (mobileNav) mobileNav.style.display = 'block';
        } else if (width >= 480 && width < 768) {
            if (mobileNav) mobileNav.style.display = 'block';
        } else if (width >= 768 && width < 1024) {
            if (tabletNav) tabletNav.style.display = 'flex';
        } else if (width >= 1024 && width < 1600) {
            if (desktopNav) desktopNav.style.display = 'flex';
        } else if (width >= 1600) {
            if (qhdNav) qhdNav.style.display = 'flex';
        }
    }
    
    // Initial setup
    updateNavigationVisibility();
    
    // Update on resize with debouncing
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(updateNavigationVisibility, 150);
    });
    
    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            const isHidden = mobileMenu.classList.contains('hidden');
            
            if (isHidden) {
                mobileMenu.classList.remove('hidden');
                mobileMenu.style.maxHeight = mobileMenu.scrollHeight + 'px';
                this.querySelector('i').classList.replace('fa-bars', 'fa-times');
                this.setAttribute('aria-expanded', 'true');
            } else {
                mobileMenu.style.maxHeight = '0';
                setTimeout(() => {
                    mobileMenu.classList.add('hidden');
                }, 300);
                this.querySelector('i').classList.replace('fa-times', 'fa-bars');
                this.setAttribute('aria-expanded', 'false');
            }
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileNav.contains(e.target) && !mobileMenu.classList.contains('hidden')) {
                mobileMenu.style.maxHeight = '0';
                setTimeout(() => {
                    mobileMenu.classList.add('hidden');
                }, 300);
                mobileMenuBtn.querySelector('i').classList.replace('fa-times', 'fa-bars');
                mobileMenuBtn.setAttribute('aria-expanded', 'false');
            }
        });
        
        // Close mobile menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !mobileMenu.classList.contains('hidden')) {
                mobileMenu.style.maxHeight = '0';
                setTimeout(() => {
                    mobileMenu.classList.add('hidden');
                }, 300);
                mobileMenuBtn.querySelector('i').classList.replace('fa-times', 'fa-bars');
                mobileMenuBtn.setAttribute('aria-expanded', 'false');
            }
        });
    }
}

// Dropdown Menu System
function initializeDropdownMenus() {
    const dropdownTriggers = document.querySelectorAll('.group');
    
    dropdownTriggers.forEach(trigger => {
        const dropdown = trigger.querySelector('.dropdown-menu, .absolute');
        
        if (dropdown) {
            let hoverTimeout;
            
            // Mouse enter
            trigger.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
                dropdown.classList.remove('hidden');
                dropdown.classList.add('block');
                
                // Animate dropdown
                dropdown.style.opacity = '0';
                dropdown.style.transform = 'translateY(-10px)';
                dropdown.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                
                setTimeout(() => {
                    dropdown.style.opacity = '1';
                    dropdown.style.transform = 'translateY(0)';
                }, 10);
            });
            
            // Mouse leave
            trigger.addEventListener('mouseleave', function() {
                hoverTimeout = setTimeout(() => {
                    dropdown.style.opacity = '0';
                    dropdown.style.transform = 'translateY(-10px)';
                    
                    setTimeout(() => {
                        dropdown.classList.add('hidden');
                        dropdown.classList.remove('block');
                    }, 300);
                }, 150);
            });
            
            // Keep dropdown open when hovering over it
            dropdown.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
            });
            
            dropdown.addEventListener('mouseleave', function() {
                hoverTimeout = setTimeout(() => {
                    dropdown.style.opacity = '0';
                    dropdown.style.transform = 'translateY(-10px)';
                    
                    setTimeout(() => {
                        dropdown.classList.add('hidden');
                        dropdown.classList.remove('block');
                    }, 300);
                }, 150);
            });
            
            // Keyboard navigation
            trigger.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    const isHidden = dropdown.classList.contains('hidden');
                    
                    if (isHidden) {
                        dropdown.classList.remove('hidden');
                        dropdown.classList.add('block');
                        dropdown.style.opacity = '1';
                        dropdown.style.transform = 'translateY(0)';
                    } else {
                        dropdown.classList.add('hidden');
                        dropdown.classList.remove('block');
                    }
                }
                
                if (e.key === 'Escape') {
                    dropdown.classList.add('hidden');
                    dropdown.classList.remove('block');
                    trigger.focus();
                }
            });
        }
    });
    
    // Close all dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        const openDropdowns = document.querySelectorAll('.dropdown-menu.block, .absolute.block');
        
        openDropdowns.forEach(dropdown => {
            const trigger = dropdown.closest('.group');
            if (trigger && !trigger.contains(e.target)) {
                dropdown.style.opacity = '0';
                dropdown.style.transform = 'translateY(-10px)';
                
                setTimeout(() => {
                    dropdown.classList.add('hidden');
                    dropdown.classList.remove('block');
                }, 300);
            }
        });
    });
}

// Navigation Indicators
function initializeNavigationIndicators() {
    const indicators = document.querySelectorAll('#nav-indicator, #qhd-nav-indicator');
    
    function updateIndicator(nav, indicator) {
        const activeLink = nav.querySelector('.nav-item.active') || nav.querySelector('.nav-item');
        
        if (activeLink && indicator) {
            const rect = activeLink.getBoundingClientRect();
            const navRect = nav.getBoundingClientRect();
            
            indicator.style.left = (rect.left - navRect.left) + 'px';
            indicator.style.width = rect.width + 'px';
            indicator.style.opacity = '1';
        }
    }
    
    // Update indicators for each navigation
    const desktopNav = document.getElementById('desktop-nav');
    const qhdNav = document.getElementById('qhd-nav');
    const navIndicator = document.getElementById('nav-indicator');
    const qhdNavIndicator = document.getElementById('qhd-nav-indicator');
    
    if (desktopNav && navIndicator) {
        updateIndicator(desktopNav, navIndicator);
    }
    
    if (qhdNav && qhdNavIndicator) {
        updateIndicator(qhdNav, qhdNavIndicator);
    }
    
    // Update on window resize
    window.addEventListener('resize', () => {
        if (desktopNav && navIndicator) {
            updateIndicator(desktopNav, navIndicator);
        }
        if (qhdNav && qhdNavIndicator) {
            updateIndicator(qhdNav, qhdNavIndicator);
        }
    });
    
    // Update when navigation items are hovered
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const nav = this.closest('nav');
            const indicator = nav.querySelector('#nav-indicator, #qhd-nav-indicator');
            
            if (indicator) {
                const rect = this.getBoundingClientRect();
                const navRect = nav.getBoundingClientRect();
                
                indicator.style.left = (rect.left - navRect.left) + 'px';
                indicator.style.width = rect.width + 'px';
                indicator.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            }
        });
        
        item.addEventListener('mouseleave', function() {
            const nav = this.closest('nav');
            const indicator = nav.querySelector('#nav-indicator, #qhd-nav-indicator');
            
            if (indicator) {
                setTimeout(() => {
                    updateIndicator(nav, indicator);
                }, 100);
            }
        });
    });
}

// Scroll-based Navigation
function initializeScrollNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-item[href^="#"], a[href^="#"]');
    
    // Smooth scroll for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                
                if (target) {
                    const navHeight = document.querySelector('nav').offsetHeight;
                    const targetPosition = target.offsetTop - navHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                    
                    // Update URL without triggering scroll
                    history.pushState(null, null, href);
                    
                    // Close mobile menu if open
                    const mobileMenu = document.getElementById('mobile-menu');
                    if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                        mobileMenu.style.maxHeight = '0';
                        setTimeout(() => {
                            mobileMenu.classList.add('hidden');
                        }, 300);
                        
                        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
                        if (mobileMenuBtn) {
                            mobileMenuBtn.querySelector('i').classList.replace('fa-times', 'fa-bars');
                            mobileMenuBtn.setAttribute('aria-expanded', 'false');
                        }
                    }
                }
            }
        });
    });
    
    // Highlight active section
    function highlightActiveSection() {
        let current = '';
        const scrollPosition = window.pageYOffset + 200;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                current = section.getAttribute('id');
            }
        });
        
        // Update active navigation items
        navLinks.forEach(link => {
            link.classList.remove('active');
            
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
        
        // Update navigation indicators
        const desktopNav = document.getElementById('desktop-nav');
        const qhdNav = document.getElementById('qhd-nav');
        const navIndicator = document.getElementById('nav-indicator');
        const qhdNavIndicator = document.getElementById('qhd-nav-indicator');
        
        if (desktopNav && navIndicator) {
            const activeLink = desktopNav.querySelector('.nav-item.active');
            if (activeLink) {
                const rect = activeLink.getBoundingClientRect();
                const navRect = desktopNav.getBoundingClientRect();
                
                navIndicator.style.left = (rect.left - navRect.left) + 'px';
                navIndicator.style.width = rect.width + 'px';
            }
        }
        
        if (qhdNav && qhdNavIndicator) {
            const activeLink = qhdNav.querySelector('.nav-item.active');
            if (activeLink) {
                const rect = activeLink.getBoundingClientRect();
                const navRect = qhdNav.getBoundingClientRect();
                
                qhdNavIndicator.style.left = (rect.left - navRect.left) + 'px';
                qhdNavIndicator.style.width = rect.width + 'px';
            }
        }
    }
    
    // Throttled scroll event
    let scrollTimeout;
    window.addEventListener('scroll', () => {
        if (!scrollTimeout) {
            scrollTimeout = setTimeout(() => {
                highlightActiveSection();
                scrollTimeout = null;
            }, 10);
        }
    });
    
    // Initial highlight
    highlightActiveSection();
}

// Breadcrumb Navigation
function initializeBreadcrumbs() {
    const breadcrumbContainer = document.querySelector('.breadcrumbs');
    
    if (breadcrumbContainer) {
        const path = window.location.pathname;
        const segments = path.split('/').filter(segment => segment);
        
        let breadcrumbHTML = '<a href="/" class="text-blue-400 hover:text-blue-300">Inicio</a>';
        
        let currentPath = '';
        segments.forEach((segment, index) => {
            currentPath += '/' + segment;
            const isLast = index === segments.length - 1;
            const displayName = segment.charAt(0).toUpperCase() + segment.slice(1).replace('-', ' ');
            
            if (isLast) {
                breadcrumbHTML += ` <i class="fas fa-chevron-right text-gray-500 mx-2"></i> <span class="text-gray-300">${displayName}</span>`;
            } else {
                breadcrumbHTML += ` <i class="fas fa-chevron-right text-gray-500 mx-2"></i> <a href="${currentPath}" class="text-blue-400 hover:text-blue-300">${displayName}</a>`;
            }
        });
        
        breadcrumbContainer.innerHTML = breadcrumbHTML;
    }
}

// Navigation accessibility improvements
function initializeNavigationAccessibility() {
    // Add ARIA labels and roles
    const navElements = document.querySelectorAll('nav');
    
    navElements.forEach((nav, index) => {
        nav.setAttribute('role', 'navigation');
        nav.setAttribute('aria-label', `Main navigation ${index + 1}`);
    });
    
    // Add keyboard navigation for dropdowns
    const dropdownTriggers = document.querySelectorAll('.group > a');
    
    dropdownTriggers.forEach(trigger => {
        trigger.setAttribute('aria-haspopup', 'true');
        trigger.setAttribute('aria-expanded', 'false');
        
        const dropdown = trigger.nextElementSibling;
        if (dropdown) {
            dropdown.setAttribute('role', 'menu');
            
            const menuItems = dropdown.querySelectorAll('a');
            menuItems.forEach(item => {
                item.setAttribute('role', 'menuitem');
            });
        }
    });
    
    // Skip link for accessibility
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Saltar al contenido principal';
    skipLink.className = 'skip-link sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded z-50';
    document.body.insertBefore(skipLink, document.body.firstChild);
}

// Initialize accessibility features
initializeNavigationAccessibility();
