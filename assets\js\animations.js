// Animations JavaScript for NoDicciSeña
document.addEventListener('DOMContentLoaded', function() {
    initializeScrollAnimations();
    initializeHoverEffects();
    initializeLoadingAnimations();
    initializeParticleSystem();
    initializeTextAnimations();
});

// Scroll-based animations
function initializeScrollAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                
                // Add animation classes based on data attributes
                if (element.hasAttribute('data-animate-slide-up')) {
                    element.classList.add('animate-slide-up');
                }
                if (element.hasAttribute('data-animate-slide-left')) {
                    element.classList.add('animate-slide-in-left');
                }
                if (element.hasAttribute('data-animate-slide-right')) {
                    element.classList.add('animate-slide-in-right');
                }
                if (element.hasAttribute('data-animate-fade')) {
                    element.classList.add('animate-fade-in');
                }
                if (element.hasAttribute('data-animate-scale')) {
                    element.classList.add('animate-scale-in');
                }
                
                // Stagger animations for child elements
                const children = element.querySelectorAll('[data-stagger]');
                children.forEach((child, index) => {
                    setTimeout(() => {
                        child.classList.add('animate-fade-in');
                    }, index * 100);
                });
                
                observer.unobserve(element);
            }
        });
    }, observerOptions);

    // Observe all elements with animation data attributes
    const animatedElements = document.querySelectorAll('[data-animate-slide-up], [data-animate-slide-left], [data-animate-slide-right], [data-animate-fade], [data-animate-scale]');
    animatedElements.forEach(el => observer.observe(el));
}

// Hover effects and interactions
function initializeHoverEffects() {
    // Card hover effects
    const cards = document.querySelectorAll('.card, .pricing-card, .team-card, .stat-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.5)';
            this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
        });
    });
    
    // Button hover effects
    const buttons = document.querySelectorAll('.btn-primary, .btn-secondary, button[class*="bg-gradient"]');
    
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
            this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.3)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
    
    // Navigation item effects
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1.2) rotate(5deg)';
                icon.style.transition = 'transform 0.3s ease';
            }
        });
        
        item.addEventListener('mouseleave', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });
    
    // Gallery item hover effects
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    galleryItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) rotateY(5deg)';
            this.style.zIndex = '10';
            this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotateY(0deg)';
            this.style.zIndex = '1';
        });
    });
}

// Loading animations
function initializeLoadingAnimations() {
    // Skeleton loading effect
    function createSkeletonLoader(element) {
        element.classList.add('skeleton-loader');
        element.style.background = 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)';
        element.style.backgroundSize = '200% 100%';
        element.style.animation = 'skeleton-loading 1.5s infinite';
    }
    
    // Add skeleton loading CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes skeleton-loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        @keyframes scale-in {
            from { 
                opacity: 0; 
                transform: scale(0.8); 
            }
            to { 
                opacity: 1; 
                transform: scale(1); 
            }
        }
        
        .animate-scale-in {
            animation: scale-in 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
    `;
    document.head.appendChild(style);
    
    // Progressive image loading
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.add('animate-fade-in');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Particle system for background effects
function initializeParticleSystem() {
    const particleContainer = document.createElement('div');
    particleContainer.className = 'fixed inset-0 pointer-events-none z-0';
    particleContainer.id = 'particle-system';
    document.body.appendChild(particleContainer);
    
    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'absolute rounded-full opacity-20';
        
        // Random properties
        const size = Math.random() * 4 + 2;
        const x = Math.random() * window.innerWidth;
        const y = window.innerHeight + 10;
        const duration = Math.random() * 10 + 10;
        const delay = Math.random() * 5;
        
        // Random color
        const colors = ['#3b82f6', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b'];
        const color = colors[Math.floor(Math.random() * colors.length)];
        
        particle.style.cssText = `
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: ${color};
            animation: float-up ${duration}s linear ${delay}s infinite;
        `;
        
        return particle;
    }
    
    // Add floating animation
    const floatStyle = document.createElement('style');
    floatStyle.textContent = `
        @keyframes float-up {
            from {
                transform: translateY(0) rotate(0deg);
                opacity: 0.2;
            }
            10% {
                opacity: 0.4;
            }
            90% {
                opacity: 0.4;
            }
            to {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(floatStyle);
    
    // Create particles periodically
    function spawnParticles() {
        if (particleContainer.children.length < 20) {
            const particle = createParticle();
            particleContainer.appendChild(particle);
            
            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.remove();
                }
            }, 20000);
        }
    }
    
    // Spawn particles every 2 seconds
    setInterval(spawnParticles, 2000);
    
    // Initial particles
    for (let i = 0; i < 5; i++) {
        setTimeout(spawnParticles, i * 400);
    }
}

// Text animations
function initializeTextAnimations() {
    // Typewriter effect
    function typeWriter(element, text, speed = 50) {
        element.textContent = '';
        let i = 0;
        
        function type() {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        
        type();
    }
    
    // Observe typewriter elements
    const typewriterElements = document.querySelectorAll('[data-typewriter]');
    const typewriterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const text = element.dataset.typewriter || element.textContent;
                const speed = parseInt(element.dataset.speed) || 50;
                
                typeWriter(element, text, speed);
                typewriterObserver.unobserve(element);
            }
        });
    });
    
    typewriterElements.forEach(el => typewriterObserver.observe(el));
    
    // Text reveal animation
    function revealText(element) {
        const text = element.textContent;
        const words = text.split(' ');
        element.innerHTML = '';
        
        words.forEach((word, index) => {
            const span = document.createElement('span');
            span.textContent = word + ' ';
            span.style.opacity = '0';
            span.style.transform = 'translateY(20px)';
            span.style.display = 'inline-block';
            span.style.transition = `all 0.6s ease ${index * 0.1}s`;
            element.appendChild(span);
            
            setTimeout(() => {
                span.style.opacity = '1';
                span.style.transform = 'translateY(0)';
            }, 100);
        });
    }
    
    // Observe text reveal elements
    const textRevealElements = document.querySelectorAll('[data-text-reveal]');
    const textRevealObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                revealText(entry.target);
                textRevealObserver.unobserve(entry.target);
            }
        });
    });
    
    textRevealElements.forEach(el => textRevealObserver.observe(el));
}

// Mouse trail effect
function initializeMouseTrail() {
    const trail = [];
    const trailLength = 10;
    
    function createTrailDot() {
        const dot = document.createElement('div');
        dot.className = 'fixed w-2 h-2 bg-blue-400 rounded-full pointer-events-none z-50 opacity-0';
        document.body.appendChild(dot);
        return dot;
    }
    
    // Initialize trail dots
    for (let i = 0; i < trailLength; i++) {
        trail.push(createTrailDot());
    }
    
    let mouseX = 0;
    let mouseY = 0;
    
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });
    
    function updateTrail() {
        trail.forEach((dot, index) => {
            const delay = index * 50;
            
            setTimeout(() => {
                dot.style.left = mouseX + 'px';
                dot.style.top = mouseY + 'px';
                dot.style.opacity = (trailLength - index) / trailLength;
                dot.style.transform = `scale(${(trailLength - index) / trailLength})`;
            }, delay);
        });
        
        requestAnimationFrame(updateTrail);
    }
    
    // Only enable on desktop
    if (window.innerWidth > 1024) {
        updateTrail();
    }
}

// Performance-aware animations
function initializePerformanceAwareAnimations() {
    // Reduce animations on low-end devices
    const isLowEndDevice = navigator.hardwareConcurrency <= 2 || 
                          navigator.deviceMemory <= 2 ||
                          /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (isLowEndDevice) {
        document.documentElement.style.setProperty('--animation-duration', '0.1s');
        document.documentElement.classList.add('reduced-animations');
    }
    
    // Pause animations when tab is not visible
    document.addEventListener('visibilitychange', () => {
        const animations = document.getAnimations();
        
        if (document.hidden) {
            animations.forEach(animation => animation.pause());
        } else {
            animations.forEach(animation => animation.play());
        }
    });
}

// Initialize performance-aware animations
initializePerformanceAwareAnimations();

// Initialize mouse trail on desktop
if (window.innerWidth > 1024) {
    initializeMouseTrail();
}
