// Main JavaScript for NoDicciSeña
document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize all components
    initializeNavigation();
    initializeHeroCarousel();
    initializeTestimonialsCarousel();
    initializeVideoGalleryCarousel();
    initializeGalleryFilters();
    initializeCounters();
    initializeScrollEffects();
    initializeModalSystem();
    initializeFormHandlers();
    
    console.log('NoDicciSeña: All components initialized successfully');
});

// Navigation System
function initializeNavigation() {
    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-bars');
            icon.classList.toggle('fa-times');
        });
    }
    
    // Navigation indicator
    const navIndicator = document.getElementById('nav-indicator');
    const qhdNavIndicator = document.getElementById('qhd-nav-indicator');
    
    function updateNavIndicator() {
        const activeLink = document.querySelector('.nav-item.active') || document.querySelector('.nav-item');
        if (activeLink && navIndicator) {
            const rect = activeLink.getBoundingClientRect();
            const navRect = activeLink.closest('nav').getBoundingClientRect();
            navIndicator.style.left = (rect.left - navRect.left) + 'px';
            navIndicator.style.width = rect.width + 'px';
        }
    }
    
    // Update indicator on page load and resize
    updateNavIndicator();
    window.addEventListener('resize', updateNavIndicator);
    
    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Active navigation highlighting
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-item[href^="#"]');
    
    function highlightActiveSection() {
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (window.pageYOffset >= sectionTop - 200) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
        
        updateNavIndicator();
    }
    
    window.addEventListener('scroll', highlightActiveSection);
}

// Hero Carousel
function initializeHeroCarousel() {
    const heroSwiper = new Swiper('.hero-swiper', {
        loop: true,
        autoplay: {
            delay: 8000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        effect: 'fade',
        fadeEffect: {
            crossFade: true
        },
        speed: 1000,
    });
    
    // Pause autoplay on hover
    const heroSection = document.querySelector('.hero-swiper');
    if (heroSection) {
        heroSection.addEventListener('mouseenter', () => {
            heroSwiper.autoplay.stop();
        });
        
        heroSection.addEventListener('mouseleave', () => {
            heroSwiper.autoplay.start();
        });
    }
}

// Testimonials Carousel
function initializeTestimonialsCarousel() {
    const testimonialsSwiper = new Swiper('.testimonials-swiper', {
        loop: true,
        autoplay: {
            delay: 6000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        breakpoints: {
            320: {
                slidesPerView: 1,
                spaceBetween: 20
            },
            768: {
                slidesPerView: 1,
                spaceBetween: 30
            },
            1024: {
                slidesPerView: 1,
                spaceBetween: 40
            }
        },
        speed: 800,
    });
}

// Video Gallery Carousel
function initializeVideoGalleryCarousel() {
    const videoGallerySwiper = new Swiper('.video-gallery-swiper', {
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        breakpoints: {
            320: {
                slidesPerView: 1,
                spaceBetween: 20
            },
            768: {
                slidesPerView: 2,
                spaceBetween: 30
            },
            1024: {
                slidesPerView: 3,
                spaceBetween: 40
            }
        },
        speed: 600,
    });
}

// Gallery Filters
function initializeGalleryFilters() {
    const filterButtons = document.querySelectorAll('.gallery-filter');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter items
            galleryItems.forEach(item => {
                if (filter === 'all' || item.classList.contains(filter)) {
                    item.style.display = 'block';
                    item.style.animation = 'fadeIn 0.5s ease-in-out';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
    
    // Gallery item click handlers
    galleryItems.forEach(item => {
        item.addEventListener('click', function() {
            openGalleryModal(this);
        });
    });
}

// Counter Animation
function initializeCounters() {
    const counters = document.querySelectorAll('[data-counter]');
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.getAttribute('data-counter'));
                const duration = 2000; // 2 seconds
                const increment = target / (duration / 16); // 60fps
                let current = 0;

                const updateCounter = () => {
                    current += increment;
                    if (current < target) {
                        counter.textContent = Math.floor(current);
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = target;
                    }
                };

                updateCounter();
                observer.unobserve(counter);
            }
        });
    }, observerOptions);

    counters.forEach(counter => observer.observe(counter));
}

// Scroll Effects
function initializeScrollEffects() {
    // Parallax effect for background elements
    const parallaxElements = document.querySelectorAll('.particle');
    
    function updateParallax() {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        
        parallaxElements.forEach(element => {
            element.style.transform = `translateY(${rate}px)`;
        });
    }
    
    // Throttle scroll events for performance
    let ticking = false;
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }
    
    window.addEventListener('scroll', () => {
        requestTick();
        ticking = false;
    });
    
    // Navbar background on scroll
    const navbars = document.querySelectorAll('nav');
    
    function updateNavbarBackground() {
        const scrolled = window.pageYOffset;
        navbars.forEach(nav => {
            if (scrolled > 100) {
                nav.style.background = 'linear-gradient(135deg, rgba(0,0,0,0.95) 0%, rgba(30,41,59,0.95) 50%, rgba(59,130,246,0.2) 100%)';
                nav.style.backdropFilter = 'blur(20px)';
            } else {
                nav.style.background = 'linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(30,41,59,0.9) 50%, rgba(59,130,246,0.1) 100%)';
                nav.style.backdropFilter = 'blur(12px)';
            }
        });
    }
    
    window.addEventListener('scroll', updateNavbarBackground);
}

// Modal System
function initializeModalSystem() {
    const modal = document.getElementById('gallery-modal');
    const closeModal = document.getElementById('close-modal');
    const modalContent = document.getElementById('modal-content');
    
    if (closeModal) {
        closeModal.addEventListener('click', closeGalleryModal);
    }
    
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeGalleryModal();
            }
        });
    }
    
    // ESC key to close modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal && !modal.classList.contains('hidden')) {
            closeGalleryModal();
        }
    });
}

function openGalleryModal(item) {
    const modal = document.getElementById('gallery-modal');
    const modalContent = document.getElementById('modal-content');
    
    if (modal && modalContent) {
        // Get item data
        const img = item.querySelector('img');
        const title = item.querySelector('h3')?.textContent || 'Imagen';
        const description = item.querySelector('p')?.textContent || '';
        
        // Create modal content
        modalContent.innerHTML = `
            <div class="relative">
                <img src="${img.src}" alt="${img.alt}" class="w-full h-auto max-h-[80vh] object-contain">
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                    <h3 class="text-white text-xl font-semibold mb-2">${title}</h3>
                    <p class="text-gray-300">${description}</p>
                </div>
            </div>
        `;
        
        modal.classList.remove('hidden');
        modal.classList.add('flex');
        document.body.style.overflow = 'hidden';
    }
}

function closeGalleryModal() {
    const modal = document.getElementById('gallery-modal');
    if (modal) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        document.body.style.overflow = 'auto';
    }
}

// Form Handlers
function initializeFormHandlers() {
    // Newsletter subscription
    const newsletterForm = document.querySelector('footer form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            
            // Show loading state
            const button = this.querySelector('button');
            const originalText = button.textContent;
            button.textContent = 'Suscribiendo...';
            button.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                // Show success message
                showNotification('¡Gracias por suscribirte! Pronto recibirás nuestras actualizaciones.', 'success');
                this.reset();
                
                // Reset button
                button.textContent = originalText;
                button.disabled = false;
            }, 1500);
        });
    }
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-[200] p-4 rounded-xl shadow-lg transform translate-x-full transition-transform duration-300 ${
        type === 'success' ? 'bg-green-600' : 
        type === 'error' ? 'bg-red-600' : 
        'bg-blue-600'
    } text-white max-w-sm`;
    
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <i class="fas ${
                type === 'success' ? 'fa-check-circle' : 
                type === 'error' ? 'fa-exclamation-circle' : 
                'fa-info-circle'
            }"></i>
            <span>${message}</span>
            <button class="ml-auto text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Performance monitoring
function logPerformance() {
    if (window.performance && window.performance.timing) {
        const timing = window.performance.timing;
        const loadTime = timing.loadEventEnd - timing.navigationStart;
        console.log(`NoDicciSeña: Page loaded in ${loadTime}ms`);
    }
}

// Log performance when page is fully loaded
window.addEventListener('load', logPerformance);

// Error handling
window.addEventListener('error', function(e) {
    console.error('NoDicciSeña Error:', e.error);
    // You could send this to an error tracking service
});

// Service Worker registration (for PWA capabilities)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('NoDicciSeña: ServiceWorker registered successfully');
            })
            .catch(function(error) {
                console.log('NoDicciSeña: ServiceWorker registration failed');
            });
    });
}
