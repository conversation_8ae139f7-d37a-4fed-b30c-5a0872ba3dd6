/* Main CSS for NoDicciSeña */

/* Custom Properties */
:root {
    --primary-blue: #3b82f6;
    --primary-purple: #8b5cf6;
    --primary-cyan: #06b6d4;
    --secondary-pink: #ec4899;
    --secondary-orange: #f97316;
    --accent-green: #10b981;
    --accent-yellow: #f59e0b;
    
    --bg-dark: #0f172a;
    --bg-darker: #020617;
    --text-light: #f8fafc;
    --text-gray: #94a3b8;
    
    --border-light: rgba(255, 255, 255, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.05);
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-light);
    background: var(--bg-dark);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    line-height: 1.2;
}

/* Utility Classes */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-light);
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple), var(--primary-cyan));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #2563eb, #7c3aed);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-secondary:hover {
    background: var(--primary-blue);
    color: white;
    transform: translateY(-2px);
}

/* Custom Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pulse-slow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes slideInLeft {
    from { 
        opacity: 0; 
        transform: translateX(-30px); 
    }
    to { 
        opacity: 1; 
        transform: translateX(0); 
    }
}

@keyframes slideInRight {
    from { 
        opacity: 0; 
        transform: translateX(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateX(0); 
    }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
    animation: pulse-slow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fade-in {
    animation: fadeIn 0.8s ease-out;
}

.animate-slide-up {
    animation: slideUp 0.8s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

/* Navigation Styles */
.nav-item {
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.nav-item:hover::before {
    left: 100%;
}

/* Card Styles */
.card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-light);
    border-radius: 20px;
    padding: 24px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Statistics Section */
.stat-card {
    transition: all 0.5s ease;
}

.stat-card:hover {
    transform: translateY(-10px) scale(1.05);
}

/* Pricing Cards */
.pricing-card {
    transition: all 0.5s ease;
}

.pricing-card:hover {
    transform: translateY(-15px);
}

/* Team Cards */
.team-card {
    transition: all 0.5s ease;
}

.team-card:hover {
    transform: translateY(-10px) rotateY(5deg);
}

/* Gallery */
.gallery-item {
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
    z-index: 10;
}

/* Swiper Customizations */
.swiper-pagination-bullet {
    background: rgba(255, 255, 255, 0.5);
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    background: var(--primary-blue);
    opacity: 1;
}

.swiper-button-next,
.swiper-button-prev {
    color: rgba(255, 255, 255, 0.7);
    background: rgba(0, 0, 0, 0.3);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    backdrop-filter: blur(10px);
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    color: white;
    background: rgba(59, 130, 246, 0.5);
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--bg-darker), var(--bg-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-darker);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2563eb, #7c3aed);
}

/* Selection Styling */
::selection {
    background: var(--primary-blue);
    color: white;
}

::-moz-selection {
    background: var(--primary-blue);
    color: white;
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

button:focus,
a:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --text-light: #ffffff;
        --text-gray: #ffffff;
        --border-light: rgba(255, 255, 255, 0.3);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    /* Already dark by default */
}

/* Light Mode Override (if needed) */
@media (prefers-color-scheme: light) {
    /* Keep dark theme as it's the design choice */
}

/* Custom Utilities */
.backdrop-blur-xl {
    backdrop-filter: blur(24px);
}

.backdrop-blur-2xl {
    backdrop-filter: blur(40px);
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.border-gradient {
    border: 1px solid;
    border-image: linear-gradient(135deg, var(--primary-blue), var(--primary-purple)) 1;
}

/* Interactive Elements */
.interactive-hover {
    transition: all 0.3s ease;
}

.interactive-hover:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Video Containers */
.video-container {
    position: relative;
    overflow: hidden;
}

.video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Skip Links */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-blue);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}
